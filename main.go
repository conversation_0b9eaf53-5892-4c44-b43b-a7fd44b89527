package main

import (
    "fmt"
    "io"
    "log"
    "net/http"
    "os"
    "path/filepath"
)

func main() {
    // 创建web目录（如果不存在）
    webDir := "./web"
    if _, err := os.Stat(webDir); os.IsNotExist(err) {
        err := os.Mkdir(webDir, 0755)
        if err != nil {
            log.Fatalf("无法创建web目录: %v", err)
        }
    }

    // 处理静态文件（HTML, JS等）
    fileServer := http.FileServer(http.Dir(webDir))
    http.Handle("/", fileServer)

    // 处理文件上传
    http.HandleFunc("/upload", uploadHandler)

    // 启动服务器
    port := 8080
    fmt.Printf("服务器启动在 http://localhost:%d\n", port)
    log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", port), nil))
}

func uploadHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method == "GET" {
        // 显示上传表单
        w.Head<PERSON>().Set("Content-Type", "text/html; charset=utf-8")
        w.Write([]byte(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>文件上传</title>
            </head>
            <body>
                <h2>文件上传</h2>
                <form method="post" enctype="multipart/form-data">
                    <input type="file" name="file">
                    <input type="submit" value="上传">
                </form>
            </body>
            </html>
        `))
    } else if r.Method == "POST" {
        // 处理文件上传
        file, header, err := r.FormFile("file")
        if err != nil {
            http.Error(w, "无法获取上传文件: "+err.Error(), http.StatusBadRequest)
            return
        }
        defer file.Close()

        // 创建目标文件
        dst, err := os.Create(filepath.Join("web", header.Filename))
        if err != nil {
            http.Error(w, "无法创建目标文件: "+err.Error(), http.StatusInternalServerError)
            return
        }
        defer dst.Close()

        // 复制文件内容
        _, err = io.Copy(dst, file)
        if err != nil {
            http.Error(w, "无法保存文件: "+err.Error(), http.StatusInternalServerError)
            return
        }

        // 返回成功信息
        w.Header().Set("Content-Type", "text/html; charset=utf-8")
        w.Write([]byte(fmt.Sprintf(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>上传成功</title>
            </head>
            <body>
                <h2>文件上传成功!</h2>
                <p>文件名: %s</p>
                <p><a href="/%s">查看文件</a></p>
                <p><a href="/upload">继续上传</a></p>
            </body>
            </html>
        `, header.Filename, header.Filename)))
    } else {
        http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
    }
}