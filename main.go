package main

import (
    "fmt"
    "io"
    "log"
    "net/http"
    "os"
    "path/filepath"
)

func main() {
    // 创建web目录（如果不存在）
    webDir := "./web"
    if _, err := os.Stat(webDir); os.IsNotExist(err) {
        err := os.Mkdir(webDir, 0755)
        if err != nil {
            log.Fatalf("无法创建web目录: %v", err)
        }
    }

    // 检查并创建默认页面
    createDefaultPageIfNeeded(webDir)

    // 处理静态文件（HTML, JS等）
    fileServer := http.FileServer(http.Dir(webDir))
    http.Handle("/", fileServer)

    // 处理文件上传
    http.HandleFunc("/upload", uploadHandler)

    // 启动服务器
    port := 8080
    fmt.Printf("服务器启动在 http://localhost:%d\n", port)
    log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", port), nil))
}

func uploadHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method == "GET" {
        // 显示上传表单
        w.Header().Set("Content-Type", "text/html; charset=utf-8")
        w.Write([]byte(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>文件上传</title>
            </head>
            <body>
                <h2>文件上传</h2>
                <form method="post" enctype="multipart/form-data">
                    <input type="file" name="file">
                    <input type="submit" value="上传">
                </form>
            </body>
            </html>
        `))
    } else if r.Method == "POST" {
        // 处理文件上传
        file, header, err := r.FormFile("file")
        if err != nil {
            http.Error(w, "无法获取上传文件: "+err.Error(), http.StatusBadRequest)
            return
        }
        defer file.Close()

        // 创建目标文件
        dst, err := os.Create(filepath.Join("web", header.Filename))
        if err != nil {
            http.Error(w, "无法创建目标文件: "+err.Error(), http.StatusInternalServerError)
            return
        }
        defer dst.Close()

        // 复制文件内容
        _, err = io.Copy(dst, file)
        if err != nil {
            http.Error(w, "无法保存文件: "+err.Error(), http.StatusInternalServerError)
            return
        }

        // 返回成功信息
        w.Header().Set("Content-Type", "text/html; charset=utf-8")
        w.Write([]byte(fmt.Sprintf(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>上传成功</title>
            </head>
            <body>
                <h2>文件上传成功!</h2>
                <p>文件名: %s</p>
                <p><a href="/%s">查看文件</a></p>
                <p><a href="/upload">继续上传</a></p>
            </body>
            </html>
        `, header.Filename, header.Filename)))
    } else {
        http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
    }
}

// createDefaultPageIfNeeded 检查并创建默认页面
func createDefaultPageIfNeeded(webDir string) {
    // 检查是否存在默认页面
    indexFiles := []string{"index.html", "index.htm"}
    hasDefaultPage := false

    for _, indexFile := range indexFiles {
        indexPath := filepath.Join(webDir, indexFile)
        if _, err := os.Stat(indexPath); err == nil {
            hasDefaultPage = true
            break
        }
    }

    // 如果没有默认页面，创建一个
    if !hasDefaultPage {
        indexPath := filepath.Join(webDir, "index.html")
        indexContent := generateDefaultPageContent()

        err := os.WriteFile(indexPath, []byte(indexContent), 0644)
        if err != nil {
            log.Printf("警告：无法创建默认页面: %v", err)
        } else {
            fmt.Println("已创建默认页面: index.html")
        }
    }
}

// generateDefaultPageContent 生成默认页面的HTML内容
func generateDefaultPageContent() string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Web文件服务器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        .feature {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
            border-radius: 5px;
        }
        .button {
            display: inline-block;
            background: #007acc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #005a9e;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
        code {
            background: #f4f4f4;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 简单Web文件服务器</h1>

        <p>欢迎使用这个简单而实用的Web文件服务器！这是一个用Go语言编写的轻量级文件管理工具。</p>

        <h2>📋 项目功能</h2>

        <div class="feature">
            <strong>📁 静态文件服务</strong><br>
            自动服务web目录下的所有文件，支持HTML、CSS、JavaScript、图片等各种文件类型。
        </div>

        <div class="feature">
            <strong>📤 文件上传</strong><br>
            通过简单的Web界面上传文件到服务器，支持各种文件格式。
        </div>

        <div class="feature">
            <strong>📂 目录浏览</strong><br>
            当没有默认页面时，自动显示目录内容，方便浏览和下载文件。
        </div>

        <div class="feature">
            <strong>🔧 自动配置</strong><br>
            自动创建必要的目录结构，无需手动配置即可使用。
        </div>

        <h2>🚀 快速开始</h2>

        <p><strong>上传文件：</strong></p>
        <a href="/upload" class="button">📤 上传文件</a>

        <p><strong>服务器信息：</strong></p>
        <ul>
            <li>服务端口: <code>8080</code></li>
            <li>文件目录: <code>./web</code></li>
            <li>上传地址: <code>/upload</code></li>
        </ul>

        <h2>💡 使用说明</h2>

        <ol>
            <li><strong>上传文件</strong>：点击上方的"上传文件"按钮，选择要上传的文件</li>
            <li><strong>访问文件</strong>：上传成功后，文件将保存在web目录下，可以直接通过URL访问</li>
            <li><strong>管理文件</strong>：所有上传的文件都会显示在主页的文件列表中</li>
        </ol>

        <h2>🛠️ 技术特性</h2>

        <ul>
            <li>使用Go语言标准库开发，无外部依赖</li>
            <li>支持多部分表单数据上传</li>
            <li>自动MIME类型检测</li>
            <li>UTF-8编码支持，完美处理中文</li>
            <li>跨平台兼容（Windows、Linux、macOS）</li>
        </ul>

        <div class="footer">
            <p>🔗 <strong>简单Web文件服务器</strong> | 基于Go语言开发</p>
            <p>开始使用：<a href="/upload" class="button">上传第一个文件</a></p>
        </div>
    </div>
</body>
</html>`
}