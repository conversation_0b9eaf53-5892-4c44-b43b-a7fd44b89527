<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Web文件服务器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        .feature {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
            border-radius: 5px;
        }
        .button {
            display: inline-block;
            background: #007acc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #005a9e;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }
        code {
            background: #f4f4f4;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 简单Web文件服务器</h1>

        <p>欢迎使用这个简单而实用的Web文件服务器！这是一个用Go语言编写的轻量级文件管理工具。</p>

        <h2>📋 项目功能</h2>

        <div class="feature">
            <strong>📁 静态文件服务</strong><br>
            自动服务web目录下的所有文件，支持HTML、CSS、JavaScript、图片等各种文件类型。
        </div>

        <div class="feature">
            <strong>📤 文件上传</strong><br>
            通过简单的Web界面上传文件到服务器，支持各种文件格式。
        </div>

        <div class="feature">
            <strong>📂 目录浏览</strong><br>
            当没有默认页面时，自动显示目录内容，方便浏览和下载文件。
        </div>

        <div class="feature">
            <strong>🔧 自动配置</strong><br>
            自动创建必要的目录结构，无需手动配置即可使用。
        </div>

        <h2>🚀 快速开始</h2>

        <p><strong>上传文件：</strong></p>
        <a href="/upload" class="button">📤 上传文件</a>

        <p><strong>服务器信息：</strong></p>
        <ul>
            <li>服务端口: <code>8080</code></li>
            <li>文件目录: <code>./web</code></li>
            <li>上传地址: <code>/upload</code></li>
        </ul>

        <h2>💡 使用说明</h2>

        <ol>
            <li><strong>上传文件</strong>：点击上方的"上传文件"按钮，选择要上传的文件</li>
            <li><strong>访问文件</strong>：上传成功后，文件将保存在web目录下，可以直接通过URL访问</li>
            <li><strong>管理文件</strong>：所有上传的文件都会显示在主页的文件列表中</li>
        </ol>

        <h2>🛠️ 技术特性</h2>

        <ul>
            <li>使用Go语言标准库开发，无外部依赖</li>
            <li>支持多部分表单数据上传</li>
            <li>自动MIME类型检测</li>
            <li>UTF-8编码支持，完美处理中文</li>
            <li>跨平台兼容（Windows、Linux、macOS）</li>
        </ul>

        <div class="footer">
            <p>🔗 <strong>简单Web文件服务器</strong> | 基于Go语言开发</p>
            <p>开始使用：<a href="/upload" class="button">上传第一个文件</a></p>
        </div>
    </div>
</body>
</html>